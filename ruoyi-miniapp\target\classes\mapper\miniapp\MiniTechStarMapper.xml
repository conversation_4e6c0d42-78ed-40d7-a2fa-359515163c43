<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniTechStarMapper">
    
    <resultMap type="MiniTechStar" id="MiniTechStarResult">
        <result property="starId"    column="star_id"    />
        <result property="name"    column="name"    />
        <result property="coverUrl"    column="cover_url"    />
        <result property="description1"    column="description_1"    />
        <result property="description2"    column="description_2"    />
        <result property="topImageUrl"    column="top_image_url"    />
        <result property="middleImageUrl"    column="middle_image_url"    />
        <result property="middleName"    column="middle_name"    />
        <result property="detailIntroduction"    column="detail_introduction"    />
        <result property="viewCount"    column="view_count"    />
        <result property="address"    column="address"    />
        <result property="email"    column="email"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniTechStarVo">
        select star_id, name, cover_url, description_1, description_2, top_image_url, middle_image_url, middle_name, detail_introduction, view_count, address, email, sort_order, status, create_by, create_time, update_by, update_time, remark from mini_tech_star
    </sql>

    <select id="selectMiniTechStarList" parameterType="MiniTechStar" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description1 != null  and description1 != ''"> and description_1 like concat('%', #{description1}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniTechStarByStarId" parameterType="Long" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where star_id = #{starId}
    </select>

    <select id="selectEnabledMiniTechStarList" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>

    <select id="selectRecommendedMiniTechStarList" resultMap="MiniTechStarResult">
        <include refid="selectMiniTechStarVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
        limit 10
    </select>
        
    <insert id="insertMiniTechStar" parameterType="MiniTechStar" useGeneratedKeys="true" keyProperty="starId">
        insert into mini_tech_star
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="coverUrl != null and coverUrl != ''">cover_url,</if>
            <if test="description1 != null and description1 != ''">description_1,</if>
            <if test="description2 != null and description2 != ''">description_2,</if>
            <if test="topImageUrl != null and topImageUrl != ''">top_image_url,</if>
            <if test="middleImageUrl != null and middleImageUrl != ''">middle_image_url,</if>
            <if test="middleName != null and middleName != ''">middle_name,</if>
            <if test="detailIntroduction != null and detailIntroduction != ''">detail_introduction,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="coverUrl != null and coverUrl != ''">#{coverUrl},</if>
            <if test="description1 != null and description1 != ''">#{description1},</if>
            <if test="description2 != null and description2 != ''">#{description2},</if>
            <if test="topImageUrl != null and topImageUrl != ''">#{topImageUrl},</if>
            <if test="middleImageUrl != null and middleImageUrl != ''">#{middleImageUrl},</if>
            <if test="middleName != null and middleName != ''">#{middleName},</if>
            <if test="detailIntroduction != null and detailIntroduction != ''">#{detailIntroduction},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniTechStar" parameterType="MiniTechStar">
        update mini_tech_star
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="coverUrl != null and coverUrl != ''">cover_url = #{coverUrl},</if>
            <if test="description1 != null and description1 != ''">description_1 = #{description1},</if>
            <if test="description2 != null and description2 != ''">description_2 = #{description2},</if>
            <if test="topImageUrl != null and topImageUrl != ''">top_image_url = #{topImageUrl},</if>
            <if test="middleImageUrl != null and middleImageUrl != ''">middle_image_url = #{middleImageUrl},</if>
            <if test="middleName != null and middleName != ''">middle_name = #{middleName},</if>
            <if test="detailIntroduction != null">detail_introduction = #{detailIntroduction},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where star_id = #{starId}
    </update>

    <delete id="deleteMiniTechStarByStarId" parameterType="Long">
        delete from mini_tech_star where star_id = #{starId}
    </delete>

    <delete id="deleteMiniTechStarByStarIds" parameterType="String">
        delete from mini_tech_star where star_id in 
        <foreach item="starId" collection="array" open="(" separator="," close=")">
            #{starId}
        </foreach>
    </delete>

</mapper> 