package com.ruoyi.miniapp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 科技之星对象 mini_tech_star
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class MiniTechStar extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 科技之星ID */
    private Long starId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 封面图片地址 */
    @Excel(name = "封面图片地址")
    private String coverUrl;

    /** 描述1（通常为公司名称） */
    @Excel(name = "描述1")
    private String description1;

    /** 描述2（通常为职位描述） */
    @Excel(name = "描述2")
    private String description2;

    /** 顶部图片地址 */
    @Excel(name = "顶部图片地址")
    private String topImageUrl;

    /** 中间图片地址 */
    @Excel(name = "中间图片地址")
    private String middleImageUrl;

    /** 中间名称 */
    @Excel(name = "中间名称")
    private String middleName;

    /** 详细介绍（富文本） */
    @Excel(name = "详细介绍")
    private String detailIntroduction;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 排序（数字越小越靠前） */
    @Excel(name = "排序", prompt = "数字越小越靠前")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setStarId(Long starId) 
    {
        this.starId = starId;
    }

    public Long getStarId() 
    {
        return starId;
    }
    
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    
    public void setCoverUrl(String coverUrl)
    {
        this.coverUrl = coverUrl;
    }

    public String getCoverUrl()
    {
        return coverUrl;
    }

    public void setDescription1(String description1)
    {
        this.description1 = description1;
    }

    public String getDescription1()
    {
        return description1;
    }

    public void setDescription2(String description2)
    {
        this.description2 = description2;
    }

    public String getDescription2()
    {
        return description2;
    }

    public void setTopImageUrl(String topImageUrl)
    {
        this.topImageUrl = topImageUrl;
    }

    public String getTopImageUrl()
    {
        return topImageUrl;
    }

    public void setMiddleImageUrl(String middleImageUrl)
    {
        this.middleImageUrl = middleImageUrl;
    }

    public String getMiddleImageUrl()
    {
        return middleImageUrl;
    }

    public void setMiddleName(String middleName)
    {
        this.middleName = middleName;
    }

    public String getMiddleName()
    {
        return middleName;
    }

    public void setDetailIntroduction(String detailIntroduction)
    {
        this.detailIntroduction = detailIntroduction;
    }

    public String getDetailIntroduction()
    {
        return detailIntroduction;
    }

    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }

    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getEmail()
    {
        return email;
    }

    public void setSortOrder(Integer sortOrder)
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("starId", getStarId())
            .append("name", getName())
            .append("coverUrl", getCoverUrl())
            .append("description1", getDescription1())
            .append("description2", getDescription2())
            .append("topImageUrl", getTopImageUrl())
            .append("middleImageUrl", getMiddleImageUrl())
            .append("middleName", getMiddleName())
            .append("detailIntroduction", getDetailIntroduction())
            .append("viewCount", getViewCount())
            .append("address", getAddress())
            .append("email", getEmail())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 