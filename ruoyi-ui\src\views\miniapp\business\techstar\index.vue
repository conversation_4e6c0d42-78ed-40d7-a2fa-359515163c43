<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="描述文本一" prop="description1">
        <el-input
          v-model="queryParams.description1"
          placeholder="请输入描述文本一"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['miniapp:techstar:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:techstar:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:techstar:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:techstar:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="techstarList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="科技之星ID" align="center" prop="starId" width="100" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="封面图片" align="center" prop="coverUrl" width="150">
        <template slot-scope="scope">
          <image-preview :src="scope.row.coverUrl" :width="80" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="顶部图片" align="center" prop="topImageUrl" width="150">
        <template slot-scope="scope">
          <image-preview :src="scope.row.topImageUrl" :width="80" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="中间图片" align="center" prop="middleImageUrl" width="150">
        <template slot-scope="scope">
          <image-preview :src="scope.row.middleImageUrl" :width="80" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="描述文本一" align="center" prop="description1" :show-overflow-tooltip="true" />
      <el-table-column label="描述文本二" align="center" prop="description2" :show-overflow-tooltip="true"/>
      <el-table-column label="浏览次数" align="center" prop="viewCount" />
      <el-table-column label="地址" align="center" prop="address" :show-overflow-tooltip="true"/>
      <el-table-column label="邮箱" align="center" prop="email" />
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['miniapp:techstar:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:techstar:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改科技之星对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverUrl">
          <image-upload v-model="form.coverUrl"/>
        </el-form-item>
        <el-form-item label="描述文本一" prop="description1">
          <el-input v-model="form.description1" type="textarea" placeholder="请输入描述文本一" />
        </el-form-item>
        <el-form-item label="描述文本二" prop="description2">
          <el-input v-model="form.description2" type="textarea" placeholder="请输入描述文本二" />
        </el-form-item>
        <el-form-item label="顶部图片" prop="topImageUrl">
          <image-upload v-model="form.topImageUrl"/>
        </el-form-item>
        <el-form-item label="中间图片" prop="middleImageUrl">
          <image-upload v-model="form.middleImageUrl"/>
        </el-form-item>
        <el-form-item label="中间名称" prop="middleName">
          <el-input v-model="form.middleName" placeholder="请输入中间名称" />
        </el-form-item>
        <el-form-item label="详细介绍" prop="detailIntroduction">
          <editor v-model="form.detailIntroduction" :min-height="200"/>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTechstar, getTechstar, delTechstar, addTechstar, updateTechstar } from "@/api/miniapp/techstar";
import Editor from '@/components/Editor';

export default {
  name: "MiniTechStar",
  dicts: ['sys_normal_disable'],
  components: {
    Editor
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 科技之星表格数据
      techstarList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        description1: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        coverUrl: [
          { required: true, message: "封面图片不能为空", trigger: "blur" }
        ],
        description1: [
          { required: true, message: "描述文本一不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询科技之星列表 */
    getList() {
      this.loading = true;
      listTechstar(this.queryParams).then(response => {
        this.techstarList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        starId: null,
        name: null,
        coverUrl: null,
        description1: null,
        description2: null,
        topImageUrl: null,
        middleImageUrl: null,
        middleName: null,
        detailIntroduction: null,
        viewCount: null,
        address: null,
        email: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.starId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加科技之星";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const starId = row.starId || this.ids
      getTechstar(starId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改科技之星";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.starId != null) {
            updateTechstar(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTechstar(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const starIds = row.starId || this.ids;
      this.$modal.confirm('是否确认删除科技之星编号为"' + starIds + '"的数据项？').then(function() {
        return delTechstar(starIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/techstar/export', {
        ...this.queryParams
      }, `techstar_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
